<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.StackPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>
<?import org.controlsfx.control.textfield.CustomTextField?>

<VBox fx:id="rootBienServicioCargado" styleClass="bien-servicio-cargado" stylesheets="@../../css/sale.css, @../../css/bienServicioCargado.css" onMouseClicked="#handleClick" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.BienServicioCargadoController">
   <children>
      <!-- Contenido básico siempre visible - Estilo Original Restaurado -->
      <AnchorPane fx:id="anchorBasicContent" maxHeight="65.0" minHeight="65.0" prefHeight="65.0">
         <children>
            <!-- Panel izquierdo: Datos del item -->
            <VBox fx:id="vbDatosItem" styleClass="item-data-panel" maxWidth="90.0" minWidth="90.0" prefWidth="90.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.topAnchor="0.0">
               <children>
                  <Label fx:id="lblGrupoItem" styleClass="grupo-item-label" maxWidth="88.0" minWidth="88.0" prefWidth="88.0" text="Rodaje" VBox.vgrow="ALWAYS">
                     <font>
                        <Font size="12.0" />
                     </font>
                     <VBox.margin>
                        <Insets top="3.0" />
                     </VBox.margin>
                  </Label>
                  <Label fx:id="lblCodCompuesto" styleClass="codigo-compuesto-label" maxWidth="88.0" minWidth="88.0" prefHeight="45.0" prefWidth="88.0" text="00158KOY" VBox.vgrow="ALWAYS">
                     <font>
                        <Font size="15.0" />
                     </font>
                  </Label>
                  <Label fx:id="lblMarca" styleClass="marca-label" maxWidth="88.0" minWidth="88.0" prefWidth="88.0" text="Koyo" VBox.vgrow="ALWAYS">
                     <font>
                        <Font size="12.0" />
                     </font>
                     <VBox.margin>
                        <Insets bottom="3.0" />
                     </VBox.margin>
                  </Label>
               </children>
            </VBox>

            <!-- Panel central: Descripción responsiva -->
            <StackPane fx:id="stackDescripcion" styleClass="description-panel" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="90.0" AnchorPane.rightAnchor="90.0" AnchorPane.topAnchor="0.0">
               <children>
                  <Label fx:id="lblDescripcionDisplay" styleClass="descripcion-label" maxWidth="Infinity" text="Descripcion Producto" StackPane.alignment="CENTER">
                     <font>
                        <Font size="14.0" />
                     </font>
                     <padding>
                        <Insets left="5.0" right="5.0" />
                     </padding>
                  </Label>
                  <CustomTextField fx:id="descripcionDelBienServicio" styleClass="bien-servicio-descripcion, label-like" maxWidth="Infinity" text="Descripcion Producto" visible="false" StackPane.alignment="CENTER">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </CustomTextField>
               </children>
            </StackPane>

            <!-- Panel derecho: Precios y cantidades -->
            <AnchorPane fx:id="anchorPrecioCantidad" styleClass="price-panel" maxHeight="65.0" maxWidth="90.0" minHeight="65.0" minWidth="90.0" prefWidth="90.0" AnchorPane.bottomAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
               <children>
                  <CustomTextField fx:id="precioAcordado" styleClass="bien-servicio-precio, label-like" text="3.00" AnchorPane.bottomAnchor="30.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="33.0" AnchorPane.topAnchor="0.0">
                     <font>
                        <Font size="14.0" />
                     </font>
                  </CustomTextField>
                  <CustomTextField fx:id="cantidad" styleClass="bien-servicio-cantidad, label-like" prefHeight="24.0" prefWidth="41.0" text="001" AnchorPane.bottomAnchor="30.0" AnchorPane.leftAnchor="58.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
                     <font>
                        <Font size="18.0" />
                     </font>
                  </CustomTextField>
                  <CustomTextField fx:id="montoAcordado" styleClass="bien-servicio-total, label-like" text="3.00" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="24.0">
                     <font>
                        <Font size="21.0" />
                     </font>
                  </CustomTextField>
               </children>
            </AnchorPane>
         </children>
      </AnchorPane>

      <!-- Panel expandido: Diseño responsivo moderno -->
      <VBox fx:id="vbExpandedContent" styleClass="expanded-content" managed="false" visible="false">
         <children>
            <!-- Contenedor principal con diseño responsivo -->
            <HBox styleClass="expanded-main-container" spacing="0.0">
               <!-- Columna izquierda: Información adicional del item -->
               <VBox styleClass="expanded-section-left">
                  <children>
                     <Label text="Detalles" styleClass="section-header-small">
                        <font>
                           <Font name="System Bold" size="10.0" />
                        </font>
                     </Label>
                     <Label fx:id="lblPrecioInicial" text="P.Inicial: 0.00" styleClass="detail-label-small">
                        <font>
                           <Font size="9.0" />
                        </font>
                     </Label>
                     <Label fx:id="lblPrecioVentaBase" text="P.Base: 0.00" styleClass="detail-label-small">
                        <font>
                           <Font size="9.0" />
                        </font>
                     </Label>
                  </children>
               </VBox>

               <!-- Columna central: Información del vehículo y códigos -->
               <VBox styleClass="expanded-section-center" HBox.hgrow="ALWAYS">
                  <children>
                     <Label text="Información del Item" styleClass="section-header-small">
                        <font>
                           <Font name="System Bold" size="10.0" />
                        </font>
                     </Label>
                     <Label fx:id="lblVehiculo" text="Vehículo: N/A" styleClass="detail-label-small">
                        <font>
                           <Font size="9.0" />
                        </font>
                     </Label>
                     <Label fx:id="lblCodigosFabrica" text="Códigos: N/A" styleClass="detail-label-small">
                        <font>
                           <Font size="9.0" />
                        </font>
                     </Label>
                     <Label fx:id="lblUbicaciones" text="Ubicaciones: N/A" styleClass="detail-label-small">
                        <font>
                           <Font size="9.0" />
                        </font>
                     </Label>
                  </children>
               </VBox>

               <!-- Columna derecha: Stock y información adicional -->
               <VBox styleClass="expanded-section-right">
                  <children>
                     <Label text="Stock" styleClass="section-header-small">
                        <font>
                           <Font name="System Bold" size="10.0" />
                        </font>
                     </Label>
                     <Label fx:id="lblStockTotal" text="Total: 0" styleClass="detail-label-small">
                        <font>
                           <Font size="9.0" />
                        </font>
                     </Label>
                     <Label fx:id="lblCargadoPor" text="Por: Usuario" styleClass="detail-label-small">
                        <font>
                           <Font size="9.0" />
                        </font>
                     </Label>
                  </children>
               </VBox>
            </HBox>
         </children>
      </VBox>
   </children>
</VBox>
