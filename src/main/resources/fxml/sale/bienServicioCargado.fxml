<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.layout.AnchorPane?>
<?import javafx.scene.layout.VBox?>
<?import javafx.scene.text.Font?>

<AnchorPane fx:id="rootBienServicioCargado" maxHeight="65.0" minHeight="65.0" prefHeight="65.0" styleClass="bien-servicio-cargado" stylesheets="@../../css/bienServicioCargado.css" onMouseClicked="#handleClick" xmlns="http://javafx.com/javafx/23.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="corp.jamaro.jamaroescritoriofx.appfx.ventas.controller.BienServicioCargadoController">
   <children>
      <VBox fx:id="vbDatosItem" alignment="CENTER" maxWidth="90.0" minWidth="90.0" prefWidth="90.0" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.topAnchor="0.0">
         <children>
            <Label fx:id="lblGrupoItem" alignment="CENTER" maxWidth="90.0" minWidth="90.0" text="Rodaje" VBox.vgrow="ALWAYS">
               <font>
                  <Font size="12.0" />
               </font>
               <VBox.margin>
                  <Insets top="3.0" />
               </VBox.margin>
            </Label>
            <Label fx:id="lblCodCompuesto" alignment="CENTER" maxWidth="90.0" minWidth="90.0" prefHeight="45.0" prefWidth="90.0" styleClass="bien-servicio-codigo" text="00158KOY" textAlignment="CENTER" VBox.vgrow="ALWAYS">
               <font>
                  <Font size="15.0" />
               </font>
            </Label>
            <Label fx:id="lblMarca" alignment="CENTER" maxWidth="90.0" minWidth="90.0" styleClass="marca-label" text="Koyo" VBox.vgrow="ALWAYS">
               <font>
                  <Font size="12.0" />
               </font>
               <VBox.margin>
                  <Insets bottom="3.0" />
               </VBox.margin>
            </Label>
         </children>
      </VBox>
      <Label fx:id="lblDescripcion" layoutX="90.0" layoutY="23.0" maxWidth="Infinity" styleClass="bien-servicio-descripcion" text="Descripcion Producto" wrapText="true" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="90.0" AnchorPane.rightAnchor="90.0" AnchorPane.topAnchor="0.0">
         <font>
            <Font size="14.0" />
         </font>
         <padding>
            <Insets left="3.0" />
         </padding>
      </Label>
      <AnchorPane fx:id="anchorPrecioCantidad" layoutX="503.0" maxHeight="65.0" maxWidth="90.0" minHeight="65.0" minWidth="90.0" prefWidth="90.0" AnchorPane.bottomAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
         <children>
            <Label fx:id="lblPrecioAcordado" alignment="CENTER" layoutY="1.0" styleClass="bien-servicio-precio" text="3.00" textAlignment="CENTER" AnchorPane.bottomAnchor="30.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="33.0" AnchorPane.topAnchor="0.0">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>
            <Label fx:id="lblCantidad" alignment="CENTER" layoutX="58.0" prefHeight="24.0" prefWidth="41.0" styleClass="bien-servicio-cantidad" text="001" textAlignment="CENTER" AnchorPane.bottomAnchor="30.0" AnchorPane.leftAnchor="58.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="0.0">
               <font>
                  <Font size="18.0" />
               </font>
            </Label>
            <Label fx:id="cantidadXPrecioAcordado" alignment="CENTER" styleClass="bien-servicio-total" text="3.00" textAlignment="CENTER" AnchorPane.bottomAnchor="0.0" AnchorPane.leftAnchor="0.0" AnchorPane.rightAnchor="0.0" AnchorPane.topAnchor="24.0">
               <font>
                  <Font size="21.0" />
               </font>
            </Label>
         </children>
      </AnchorPane>
   </children>
</AnchorPane>
