/*
 * JavaFX CSS - Hoja de estilos específica para BienServicioCargado
 * Este archivo contiene estilos para:
 * - BienServicioCargadoController
 * - bienServicioCargado.fxml
 * - Versión simplificada sin expansión
 */

/* Importar variables de tamaños de fuente */
@import "font-sizes.css";

/* Importar variables globales de colores desde styles.css */
@import "styles.css";

/* ===== Estilos principales para BienServicioCargado (Simplificado) ===== */
.bien-servicio-cargado {
    -fx-background-color: derive(-fx-primary-color-dark, 20%);
    -fx-padding: 8px 5px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-border-color: derive(-fx-secondary-color, -15%);
    -fx-border-width: 2px;
    /* Efecto de sombra más pronunciado para mejor diferenciación */
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.6), 10, 0, 0, 3);
}

/* Estado hover para la tarjeta - mejor contraste */
.bien-servicio-cargado:hover {
    -fx-background-color: derive(-fx-primary-color-dark, 35%);
    -fx-border-color: derive(-fx-accent-color, 10%);
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.8), 12, 0, 0, 4);
    -fx-cursor: hand;
}

/* Estado seleccionado para mejor diferenciación */
.bien-servicio-cargado:focused {
    -fx-background-color: derive(-fx-accent-color, 20%);
    -fx-border-color: -fx-accent-color;
    -fx-border-width: 3px;
}

/* ===== Estilos para labels y texto (Simplificado) ===== */

/* Labels específicos con mejor contraste de colores */
.bien-servicio-codigo {
    -fx-text-fill: derive(-fx-accent-color, 20%);
    -fx-font-size: 15px;
    -fx-font-weight: bold;
    -fx-text-alignment: center;
}

.marca-label {
    -fx-text-fill: derive(-fx-info-color, 15%);
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-alignment: center;
}

.bien-servicio-descripcion {
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-alignment: center;
    -fx-wrap-text: true;
}

.bien-servicio-precio {
    -fx-text-fill: derive(-fx-warning-color, 10%);
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-alignment: center;
}

.bien-servicio-cantidad {
    -fx-text-fill: derive(-fx-secondary-color, 25%);
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-alignment: center;
}

.bien-servicio-total {
    -fx-text-fill: derive(-fx-success-color, 15%);
    -fx-font-size: 21px;
    -fx-font-weight: bold;
    -fx-text-alignment: center;
}

/* ===== Estilos para stock con error ===== */
.stock-error {
    -fx-background-color: derive(-fx-error-color, 40%);
    -fx-border-color: derive(-fx-error-color, -10%);
}

.stock-error:hover {
    -fx-background-color: derive(-fx-error-color, 50%);
    -fx-border-color: -fx-error-color;
}

/* ===== Estilos para menú contextual ===== */
.context-menu {
    -fx-background-color: derive(-fx-primary-color-dark, 10%);
    -fx-border-color: derive(-fx-secondary-color, -20%);
    -fx-border-width: 1px;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.4), 8, 0, 0, 2);
}

.context-menu .menu-item {
    -fx-background-color: transparent;
    -fx-text-fill: -fx-light-color;
    -fx-padding: 8px 12px;
}

.context-menu .menu-item:hover {
    -fx-background-color: derive(-fx-secondary-color, 20%);
    -fx-text-fill: -fx-primary-color-dark;
}

.context-menu .menu-item:focused {
    -fx-background-color: derive(-fx-accent-color, 20%);
    -fx-text-fill: -fx-primary-color-dark;
}

/* Estilo específico para el item de eliminar */
.delete-menu-item {
    -fx-text-fill: -fx-error-color;
}

.delete-menu-item:hover {
    -fx-background-color: derive(-fx-error-color, 40%);
    -fx-text-fill: white;
}
