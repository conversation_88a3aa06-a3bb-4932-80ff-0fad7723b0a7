/*
 * JavaFX CSS - Hoja de estilos específica para BienServicioCargado
 * Este archivo contiene estilos para:
 * - BienServicioCargadoController
 * - bienServicioCargado.fxml
 * - Elementos expandibles y responsivos
 * - Menú contextual
 * - Restaura el estilo original con mejoras para expansión horizontal
 */

/* Importar variables de tamaños de fuente */
@import "font-sizes.css";

/* Importar variables globales de colores desde styles.css */
@import "styles.css";

/* ===== Estilos principales para BienServicioCargado (Estilo Original Restaurado) ===== */
.bien-servicio-cargado {
    -fx-background-color: derive(-fx-primary-color-dark, 15%);
    -fx-padding: 5px 2px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-border-color: derive(-fx-secondary-color, -20%);
    -fx-border-width: 1.5px;
    /* Agregamos un sutil efecto de sombra para dar apariencia de tarjeta */
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.4), 8, 0, 0, 2);
    /* Eliminamos transiciones para mantener estilo original */
    -fx-transition: none;
}

/* Estado hover para la tarjeta - sin cambios de tamaño */
.bien-servicio-cargado:hover {
    -fx-background-color: derive(-fx-primary-color-dark, 25%);
    -fx-border-color: derive(-fx-secondary-color, -10%);
    -fx-effect: none;
    -fx-cursor: hand;
    /* Aseguramos que no haya cambios de tamaño */
    -fx-scale-x: 1.0;
    -fx-scale-y: 1.0;
    -fx-scale-z: 1.0;
    -fx-transition: none;
}

/* Estado expandido para la tarjeta */
.bien-servicio-cargado-expanded {
    -fx-background-color: derive(-fx-primary-color-dark, 20%);
    -fx-border-color: derive(-fx-secondary-color, -10%);
    -fx-border-width: 1.5px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.4), 8, 0, 0, 2);
    -fx-transition: none;
}

/* ===== Estilos para el contenido básico ===== */
.basic-content {
    -fx-spacing: 0;
    -fx-alignment: center;
}

/* Panel izquierdo - Datos del item */
.item-data-panel {
    -fx-alignment: center;
    -fx-spacing: 2;
    -fx-padding: 5px;
}

/* Panel central - Descripción responsiva */
.description-panel {
    -fx-alignment: center;
    -fx-padding: 5px 10px;
}

/* Panel derecho - Precios y cantidades */
.price-panel {
    -fx-alignment: center;
    -fx-spacing: 2;
    -fx-padding: 5px;
}

/* ===== Estilos para el contenido expandido (HBox Layout) ===== */
.expanded-content {
    -fx-background-color: derive(-fx-primary-color-dark, 8%);
    -fx-border-color: derive(-fx-secondary-color, -15%);
    -fx-border-width: 1 0 0 0;
    -fx-background-radius: 0 0 8 8;
    -fx-padding: 8px 5px;
    -fx-spacing: 10;
    /* Permitir que la altura se ajuste dinámicamente */
    -fx-pref-height: -1;
    -fx-min-height: -1;
    -fx-max-height: -1;
    -fx-transition: none;
    -fx-effect: none;
}

/* Contenedor principal del contenido expandido - HBox */
.expanded-main-container {
    -fx-spacing: 10;
    -fx-alignment: center_left;
    -fx-transition: none;
    -fx-effect: none;
}

/* Secciones del contenido expandido para HBox */
.expanded-section {
    -fx-alignment: top_left;
    -fx-spacing: 3;
    -fx-padding: 3px;
    -fx-transition: none;
    -fx-effect: none;
}

.expanded-section-left {
    -fx-alignment: top_left;
    -fx-spacing: 3;
    -fx-padding: 3px;
    -fx-min-width: 80;
    -fx-pref-width: 80;
    -fx-transition: none;
    -fx-effect: none;
}

.expanded-section-center {
    -fx-alignment: top_left;
    -fx-spacing: 3;
    -fx-padding: 3px 8px;
    -fx-transition: none;
    -fx-effect: none;
}

.expanded-section-right {
    -fx-alignment: top_left;
    -fx-spacing: 3;
    -fx-padding: 3px;
    -fx-min-width: 80;
    -fx-pref-width: 80;
    -fx-transition: none;
    -fx-effect: none;
}

/* ===== Estilos para labels y texto ===== */
.section-header {
    -fx-text-fill: -fx-secondary-color;
    -fx-font-weight: bold;
    -fx-font-size: 11px;
}

.section-header-small {
    -fx-text-fill: -fx-secondary-color;
    -fx-font-weight: bold;
    -fx-font-size: 10px;
}

.detail-label {
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 10px;
    -fx-wrap-text: true;
}

.detail-label-small {
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 9px;
    -fx-wrap-text: true;
}

/* Labels específicos (Estilo Original Restaurado) */
.grupo-item-label {
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 12px;
    -fx-font-weight: normal;
    -fx-wrap-text: true;
    -fx-text-alignment: center;
    -fx-transition: none;
    -fx-effect: none;
}

.codigo-compuesto-label {
    -fx-text-fill: -fx-secondary-color;
    -fx-font-size: 15px;
    -fx-font-weight: bold;
    -fx-wrap-text: true;
    -fx-text-alignment: center;
    -fx-transition: none;
    -fx-effect: none;
}

.marca-label {
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-wrap-text: true;
    -fx-text-alignment: center;
    -fx-transition: none;
    -fx-effect: none;
}

.descripcion-label {
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-wrap-text: true;
    -fx-text-alignment: center;
    -fx-transition: none;
    -fx-effect: none;
}

/* ===== Estilos para campos editables (CustomTextField) - Estilo Original ===== */
.label-like {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-padding: 0;
    -fx-cursor: default;
    -fx-focus-color: transparent;
    -fx-faint-focus-color: transparent;
    -fx-text-box-border: transparent;
    -fx-control-inner-background: transparent;
    -fx-background-radius: 0;
    -fx-border-radius: 0;
    -fx-transition: none;
    -fx-effect: none;
}

.label-like:hover {
    -fx-background-color: transparent;
    -fx-border-color: transparent;
    -fx-effect: none;
    -fx-transition: none;
}

.label-like:focused {
    -fx-background-color: -fx-control-inner-background;
    -fx-border-color: -fx-accent;
    -fx-border-width: 1;
    -fx-padding: 2;
    -fx-cursor: text;
    -fx-background-radius: 3;
    -fx-border-radius: 3;
}

/* Descripción editable - Estilo Original */
.bien-servicio-descripcion.label-like {
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-alignment: center;
    -fx-padding: 0;
    -fx-wrap-text: true;
    -fx-transition: none;
    -fx-effect: none;
}

/* Precio acordado editable - Estilo Original */
.bien-servicio-precio.label-like {
    -fx-text-fill: -fx-info-color;
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-alignment: center;
    -fx-pref-width: 57;
    -fx-max-width: 57;
    -fx-padding: 0;
    -fx-transition: none;
    -fx-effect: none;
}

/* Cantidad editable - Estilo Original */
.bien-servicio-cantidad.label-like {
    -fx-text-fill: -fx-light-color;
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-alignment: center;
    -fx-pref-width: 41;
    -fx-max-width: 41;
    -fx-padding: 0;
    -fx-transition: none;
    -fx-effect: none;
}

/* Monto acordado editable - Estilo Original */
.bien-servicio-total.label-like {
    -fx-text-fill: -fx-success-color;
    -fx-font-size: 21px;
    -fx-font-weight: bold;
    -fx-alignment: center;
    -fx-pref-width: 90;
    -fx-max-width: 90;
    -fx-padding: 0;
    -fx-transition: none;
    -fx-effect: none;
}

/* ===== Estilos para menú contextual ===== */
.context-menu {
    -fx-background-color: derive(-fx-primary-color-dark, 10%);
    -fx-border-color: derive(-fx-secondary-color, -20%);
    -fx-border-width: 1px;
    -fx-border-radius: 5px;
    -fx-background-radius: 5px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.4), 8, 0, 0, 2);
}

.context-menu .menu-item {
    -fx-background-color: transparent;
    -fx-text-fill: -fx-light-color;
    -fx-padding: 8px 12px;
}

.context-menu .menu-item:hover {
    -fx-background-color: derive(-fx-secondary-color, 20%);
    -fx-text-fill: -fx-primary-color-dark;
}

.context-menu .menu-item:focused {
    -fx-background-color: derive(-fx-accent-color, 20%);
    -fx-text-fill: -fx-primary-color-dark;
}

/* Estilo específico para el item de eliminar */
.delete-menu-item {
    -fx-text-fill: -fx-error-color;
}

.delete-menu-item:hover {
    -fx-background-color: derive(-fx-error-color, 40%);
    -fx-text-fill: white;
}

/* ===== Estilos responsivos ===== */
@media (max-width: 800px) {
    .bien-servicio-cargado {
        -fx-padding: 6px 3px;
    }
    
    .expanded-content {
        -fx-padding: 8px 3px;
    }
    
    .expanded-section-left,
    .expanded-section-right {
        -fx-min-width: 80;
        -fx-pref-width: 80;
    }
}

@media (max-width: 600px) {
    .codigo-compuesto-label {
        -fx-font-size: 13px;
    }
    
    .descripcion-label {
        -fx-font-size: 12px;
    }
    
    .bien-servicio-total.label-like {
        -fx-font-size: 18px;
    }
}
